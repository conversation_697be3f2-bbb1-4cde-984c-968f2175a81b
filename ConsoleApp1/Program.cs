using System;

namespace DateRangesIntersectTest
{
    // Mock AlertLog class for testing
    public class AlertLog
    {
        public DateTime StartedAt { get; set; }
        public DateTime? EndedAt { get; set; }

        public AlertLog(DateTime startedAt, DateTime? endedAt = null)
        {
            StartedAt = startedAt;
            EndedAt = endedAt;
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing DateRangesIntersect method");
            Console.WriteLine("==================================");

            // Test cases
            TestCase1_NullEndDate();
            TestCase2_WithinTolerance();
            TestCase3_OutsideTolerance();
            TestCase4_ExactlyAtTolerance();
            TestCase5_NewStartBeforeOldEnd();

            Console.WriteLine("\nAll tests completed.");
            Console.ReadLine();
        }

        private static bool DateRangesIntersect(AlertLog lastAlertLog, DateTime newStart, DateTime newEnd)
        {
            if (lastAlertLog.EndedAt.HasValue is false) return true;
        
            var oldEnd = lastAlertLog.EndedAt.Value;
            var tolerance = TimeSpan.FromSeconds(90);
            
            return (newStart - oldEnd <= tolerance);
        }

        static void TestCase1_NullEndDate()
        {
            var alertLog = new AlertLog(DateTime.Now.AddHours(-2), null);
            var newStart = DateTime.Now;
            var newEnd = DateTime.Now.AddHours(1);
            
            bool result = DateRangesIntersect(alertLog, newStart, newEnd);
            
            Console.WriteLine($"Test 1 (Null End Date): {(result ? "PASS" : "FAIL")} - Expected: True, Got: {result}");
        }

        static void TestCase2_WithinTolerance()
        {
            var oldEnd = DateTime.Now.AddMinutes(-1); // 1 minute ago
            var alertLog = new AlertLog(DateTime.Now.AddHours(-1), oldEnd);
            var newStart = DateTime.Now; // Now (within 90 seconds tolerance)
            var newEnd = DateTime.Now.AddHours(1);
            
            bool result = DateRangesIntersect(alertLog, newStart, newEnd);
            
            Console.WriteLine($"Test 2 (Within Tolerance): {(result ? "PASS" : "FAIL")} - Expected: True, Got: {result}");
            Console.WriteLine($"  Time difference: {(newStart - oldEnd).TotalSeconds} seconds (tolerance: 90 seconds)");
        }

        static void TestCase3_OutsideTolerance()
        {
            var oldEnd = DateTime.Now.AddMinutes(-2); // 2 minutes ago
            var alertLog = new AlertLog(DateTime.Now.AddHours(-1), oldEnd);
            var newStart = DateTime.Now; // Now (outside 90 seconds tolerance)
            var newEnd = DateTime.Now.AddHours(1);
            
            bool result = DateRangesIntersect(alertLog, newStart, newEnd);
            
            Console.WriteLine($"Test 3 (Outside Tolerance): {(!result ? "PASS" : "FAIL")} - Expected: False, Got: {result}");
            Console.WriteLine($"  Time difference: {(newStart - oldEnd).TotalSeconds} seconds (tolerance: 90 seconds)");
        }

        static void TestCase4_ExactlyAtTolerance()
        {
            var oldEnd = DateTime.Now.AddSeconds(-90); // Exactly 90 seconds ago
            var alertLog = new AlertLog(DateTime.Now.AddHours(-1), oldEnd);
            var newStart = DateTime.Now; // Now (exactly at tolerance limit)
            var newEnd = DateTime.Now.AddHours(1);
            
            bool result = DateRangesIntersect(alertLog, newStart, newEnd);
            
            Console.WriteLine($"Test 4 (Exactly At Tolerance): {(result ? "PASS" : "FAIL")} - Expected: True, Got: {result}");
            Console.WriteLine($"  Time difference: {(newStart - oldEnd).TotalSeconds} seconds (tolerance: 90 seconds)");
        }

        static void TestCase5_NewStartBeforeOldEnd()
        {
            var oldEnd = DateTime.Now.AddMinutes(5); // 5 minutes in the future
            var alertLog = new AlertLog(DateTime.Now.AddHours(-1), oldEnd);
            var newStart = DateTime.Now; // Now (before old end)
            var newEnd = DateTime.Now.AddHours(1);
            
            bool result = DateRangesIntersect(alertLog, newStart, newEnd);
            
            Console.WriteLine($"Test 5 (New Start Before Old End): {(result ? "PASS" : "FAIL")} - Expected: True, Got: {result}");
            Console.WriteLine($"  Time difference: {(newStart - oldEnd).TotalSeconds} seconds (negative means overlap)");
        }
    }
}